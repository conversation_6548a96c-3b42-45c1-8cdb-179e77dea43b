jQuery(document).ready(function($) {
    $('#start-import').on('click', function() {
        $('#dd-status').text('Rozpoczynam import...');
        $('#import-result').hide();
        $('#update-result').hide();
        $.post(xmlImporterAjax.ajax_url, {
            action: 'xml_importer_import',
            nonce: xmlImporterAjax.nonce
        }, function(response) {
            if (response.success) {
                $('#dd-status').text('Import zakończony');
                $('#in-xml').text(response.data.inXml);
                $('#skipped').text(response.data.skipped);
                $('#failed').text(response.data.failed);
                $('#import-result').show();
            } else {
                $('#dd-status').text('Błąd importowania');
                alert('Błąd importu: ' + response.data.message);
            }
        })
            .fail(function (){
                $('#dd-status').text('Błąd importowania');
                alert('Błąd importu: ' + response.data);
            })
    });

    $('#start-update').on('click', function() {
        $('#dd-status').text('Rozpoczynam aktualizację...');
        $('#import-result').hide();
        $('#update-result').hide();
        $.post(xmlImporterAjax.ajax_url, {
            action: 'xml_updater_update',
            nonce: xmlImporterAjax.updateNonce
        }, function(response) {
            if (response.success) {
                $('#dd-status').text('Aktualizacja zakończona');
                $('#update-in-xml').text(response.data.inXml);
                $('#updated').text(response.data.updated);
                $('#update-skipped').text(response.data.skipped);
                $('#update-failed').text(response.data.failed);
                $('#not-found').text(response.data.notFoundInDatabase);
                $('#update-result').show();
            } else {
                $('#dd-status').text('Błąd aktualizacji');
                alert('Błąd aktualizacji: ' + response.data.message);
            }
        })
            .fail(function (){
                $('#dd-status').text('Błąd aktualizacji');
                alert('Błąd aktualizacji: ' + response.data);
            })
    });
});
