<?php

namespace DD\App\XmlImporter;

class XmlImporterAction
{
    public const XML_IMPORT_URL_OPTION = 'dd_import_xml_url';
    public const XML_AUTO_UPDATING_OPTION = 'dd_auto_update_xml';

    public static function init(): void
    {
        add_action('admin_menu', function () {
            add_submenu_page(
                'edit.php?post_type=' . DD_POST_TYPE,
                'Import z XML',
                'Import z XML',
                'manage_options',
                'dd-import-xml',
                [self::class, 'importPage']
            );
        });
        add_action('wp_ajax_xml_importer_import', [self::class, 'ajaxImporterAction']);
    }

    public static function importPage(): void
    {
        if (get_option(self::XML_IMPORT_URL_OPTION) === false) {
            add_option(self::XML_IMPORT_URL_OPTION, '');
        }

        if (get_option(self::XML_AUTO_UPDATING_OPTION) === false) {
            add_option(self::XML_AUTO_UPDATING_OPTION, false);
        }

        // Obsługa formularza zmiany wartości
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST[self::XML_IMPORT_URL_OPTION])) {
            update_option(self::XML_IMPORT_URL_OPTION, sanitize_text_field($_POST[self::XML_IMPORT_URL_OPTION]));
            echo '<div class="updated"><p>URL został zaktualizowany.</p></div>';
        }
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            update_option(self::XML_AUTO_UPDATING_OPTION, (bool) ($_POST[self::XML_AUTO_UPDATING_OPTION] ?? false));
            echo '<div class="updated"><p>Zmieniono ustawienie automatycznych aktualizacji z XML</p></div>';
        }

        // Pobranie aktualnej wartości
        $current_value = get_option(self::XML_IMPORT_URL_OPTION);
        $auto_update = (bool) get_option(self::XML_AUTO_UPDATING_OPTION);

        echo '<h1>Import z pliku XML</h1>';
        echo '<p>Importowanie listy mieszkań z pliku w systemie VOX Deweloper</p>';
        echo '
        <form method="post">
            <label for="' . self::XML_IMPORT_URL_OPTION . '">Podaj URL pliku XML:</label>
            <input type="text" name="' . self::XML_IMPORT_URL_OPTION . '" id="' . self::XML_IMPORT_URL_OPTION . '" value="' . esc_attr($current_value) . '" style="width: 100%; max-width: 400px;">
            <button type="submit">Zapisz</button>
        </form>
        ';
        echo '<br/><br/><button id="start-import" class="button button-primary">Rozpocznij import</button>';
        echo '<button id="start-update" class="button button-secondary" style="margin-left: 10px;">Aktualizuj dane z XML</button>';
        echo '<p id="dd-status"></p>';
        echo '<table id="import-result" class="widefat" style="margin-top:20px; display:none;">
            <thead>
                <tr><th>W XML</th><th>Pominięte</th><th>Nieudane</th></tr>
            </thead>
            <tbody>
                <tr><td id="in-xml"></td><td id="skipped"></td><td id="failed"></td></tr>
            </tbody>
          </table>';
        echo '<table id="update-result" class="widefat" style="margin-top:20px; display:none;">
            <thead>
                <tr><th>W XML</th><th>Zaktualizowane</th><th>Pominięte</th><th>Nieudane</th><th>Nie znaleziono w bazie</th></tr>
            </thead>
            <tbody>
                <tr><td id="update-in-xml"></td><td id="updated"></td><td id="update-skipped"></td><td id="update-failed"></td><td id="not-found"></td></tr>
            </tbody>
          </table>';
        echo '</div>';
        echo '
        <form method="post">
            <label for="' . self::XML_AUTO_UPDATING_OPTION . '">Czy włączyć automatyczne aktualizacje z XML?</label>
            <input type="checkbox" name="' . self::XML_AUTO_UPDATING_OPTION . '" id="' . self::XML_AUTO_UPDATING_OPTION . '" ' . ($auto_update ? 'checked' : '') . '>
            <button type="submit">Zapisz</button>
        </form>
        ';
    }

    public static function ajaxImporterAction(): void
    {
        check_ajax_referer('xml_importer_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Brak uprawnień'], 403);
        }

        $importer = new XmlImporter();
        $result = $importer->import();

        wp_send_json_success([
            'inXml' => $result->inXml,
            'skipped' => $result->skipped,
            'failed' => $result->failed
        ]);
    }
}