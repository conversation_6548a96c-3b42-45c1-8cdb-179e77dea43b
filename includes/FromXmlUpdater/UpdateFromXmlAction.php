<?php

namespace DD\App\FromXmlUpdater;

use DD\App\XmlImporter\XmlImporterAction;

class UpdateFromXmlAction
{
    private const HOOK_NAME = 'dd_update_from_xml_action';

    public static function init(): void
    {
        add_action(self::HOOK_NAME, [self::class, 'execute']);
        add_action('wp_ajax_xml_updater_update', [self::class, 'ajaxUpdaterAction']);

        if (!wp_next_scheduled( self::HOOK_NAME ) ) {
            wp_schedule_event(1735686000, 'twicedaily', self::HOOK_NAME );
        }
    }

    public static function execute(): void
    {
        $updatingEnabled = (bool) get_option(XmlImporterAction::XML_AUTO_UPDATING_OPTION);

        if ($updatingEnabled === false) {
            return;
        }

        $updater = new FromXmlUpdater();
        $updater->update();
    }

    public static function ajaxUpdaterAction(): void
    {
        check_ajax_referer('xml_updater_nonce', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => 'Brak uprawnień'], 403);
        }

        $updater = new FromXmlUpdater();
        $result = $updater->update();

        wp_send_json_success([
            'inXml' => $result->inXml,
            'updated' => $result->updated,
            'skipped' => $result->skipped,
            'failed' => $result->failed,
            'notFoundInDatabase' => $result->notFoundInDatabase
        ]);
    }
}