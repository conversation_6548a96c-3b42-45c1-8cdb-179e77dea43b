#!/bin/bash
# Wczytaj zmienne z pliku .env
if [ -f ../.env ]; then
    export $(grep -v '^#' ../.env | xargs)
else
    echo "Brak pliku .env w głównym folderze wtyczki!"
    exit 1
fi

# Sprawd<PERSON><PERSON>, czy zmienna SERVICE_NAME jest ustawiona
if [ -z "$SERVICE_NAME" ]; then
    echo "Brak zmiennej SERVICE_NAME w pliku .env!"
    exit 1
fi

if [ -d "builds" ]; then
    echo "Folder builds istnieje."
else
    mkdir "builds"
fi

# Instalacja zależności Composer
cd ../
composer install --no-dev

# Tworzenie pliku zip
ZIP_FILE="$SERVICE_NAME.zip"

echo "Tworzenie archiwum: $ZIP_FILE"

zip -r "setup/builds/$ZIP_FILE" ./ -x ".git/*" "composer.json" "composer.lock" "setup/*" "test/*" "tests/*" "phpstan.neon" "phpstan-baseline.neon" ".gitmodules" ".gitignore" ".idea/*" ".github/*" ".env" ".gitattributes" "readme.md" "phpunit.xml" "README.md" "INSTALL.md"

composer install

echo "Gotowe!"
